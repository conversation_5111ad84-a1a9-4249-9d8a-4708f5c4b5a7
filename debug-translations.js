// Debug script to test sync checkbox translations
console.log('=== Sync Checkbox Translation Debug ===');

// Check if the sync checkbox exists
const syncCheckbox = document.getElementById('sync-auto-checkbox');
console.log('Sync checkbox found:', !!syncCheckbox);

if (syncCheckbox) {
    const label = syncCheckbox.closest('.checkbox-label');
    console.log('Checkbox label found:', !!label);
    
    if (label) {
        const span = label.querySelector('span[data-i18n="sync.autoSync"]');
        console.log('Span with data-i18n found:', !!span);
        
        if (span) {
            console.log('Current span text:', span.textContent);
            console.log('Data-i18n attribute:', span.getAttribute('data-i18n'));
        }
    }
}

// Check if sync auto option is visible
const syncAutoOption = document.getElementById('sync-auto-option');
console.log('Sync auto option found:', !!syncAutoOption);
if (syncAutoOption) {
    console.log('Sync auto option display:', window.getComputedStyle(syncAutoOption).display);
}

// Check if translations are loaded
console.log('i18nModule available:', !!window.i18nModule);
if (window.i18nModule) {
    console.log('Current language:', window.i18nModule.currentLanguage);
    console.log('Sync translations available:', !!window.i18nModule.translations.sync);
    
    if (window.i18nModule.translations.sync) {
        console.log('autoSync translation:', window.i18nModule.translations.sync.autoSync);
        console.log('autoSyncNote translation:', window.i18nModule.translations.sync.autoSyncNote);
    }
}

// Check if applyTranslations function is available
console.log('applyTranslations function available:', typeof window.applyTranslations);

// Test translation function
if (window.getNestedTranslation && window.i18nModule) {
    const autoSyncTranslation = window.getNestedTranslation(window.i18nModule.translations, 'sync.autoSync');
    console.log('getNestedTranslation result for sync.autoSync:', autoSyncTranslation);
}

console.log('=== End Debug ===');
