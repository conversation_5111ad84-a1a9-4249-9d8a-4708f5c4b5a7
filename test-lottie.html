<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Loader Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .loader-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .scramble-loader {
            display: inline-block;
            width: 30px;
            height: 30px;
            margin: 0 auto;
            vertical-align: middle;
        }
        .fmc-loading-spinner {
            width: 32px;
            height: 32px;
            display: inline-block;
        }
        /* Fallback styles */
        .scramble-loader.fallback {
            border: 3px solid rgba(165, 120, 101, 0.3);
            border-radius: 50%;
            border-top-color: #a57865;
            animation: spin 1s ease-in-out infinite;
        }
        .fmc-loading-spinner.fallback {
            border: 3px solid #f0f0f0;
            border-top: 3px solid #a57865;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        button {
            background: #a57865;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8b6a57;
        }
    </style>
</head>
<body>
    <h1>Lottie Loader Test</h1>
    
    <div class="test-container">
        <h2>Scramble Loader Test</h2>
        <div class="loader-container">
            <div class="scramble-loader" id="test-scramble-loader"></div>
            <span>Generating scramble...</span>
        </div>
        <button onclick="testScrambleLoader()">Test Scramble Loader</button>
        <button onclick="stopScrambleLoader()">Stop Loader</button>
    </div>

    <div class="test-container">
        <h2>FMC Loader Test</h2>
        <div class="loader-container">
            <div class="fmc-loading-spinner" id="test-fmc-loader"></div>
            <span>Loading FMC...</span>
        </div>
        <button onclick="testFMCLoader()">Test FMC Loader</button>
        <button onclick="stopFMCLoader()">Stop Loader</button>
    </div>

    <div class="test-container">
        <h2>Sync Loader Test</h2>
        <div class="loader-container">
            <div class="scramble-loader" id="test-sync-loader"></div>
            <span>Syncing...</span>
        </div>
        <button onclick="testSyncLoader()">Test Sync Loader</button>
        <button onclick="stopSyncLoader()">Stop Loader</button>
    </div>

    <!-- Lottie Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
    
    <script type="module">
        import { insertLottieLoader } from './assets/js/lottie-loader.js';
        
        let scrambleAnimation = null;
        let fmcAnimation = null;
        let syncAnimation = null;
        
        window.testScrambleLoader = async function() {
            const container = document.getElementById('test-scramble-loader');
            try {
                scrambleAnimation = await insertLottieLoader(container, {
                    width: 30,
                    height: 30,
                    loop: true,
                    autoplay: true
                });
                console.log('Scramble loader started successfully');
            } catch (error) {
                console.error('Failed to start scramble loader:', error);
                container.classList.add('fallback');
            }
        };
        
        window.stopScrambleLoader = function() {
            const container = document.getElementById('test-scramble-loader');
            if (scrambleAnimation) {
                scrambleAnimation.destroy();
                scrambleAnimation = null;
            }
            container.innerHTML = '';
            container.classList.remove('fallback');
        };
        
        window.testFMCLoader = async function() {
            const container = document.getElementById('test-fmc-loader');
            try {
                fmcAnimation = await insertLottieLoader(container, {
                    width: 32,
                    height: 32,
                    loop: true,
                    autoplay: true
                });
                console.log('FMC loader started successfully');
            } catch (error) {
                console.error('Failed to start FMC loader:', error);
                container.classList.add('fallback');
            }
        };
        
        window.stopFMCLoader = function() {
            const container = document.getElementById('test-fmc-loader');
            if (fmcAnimation) {
                fmcAnimation.destroy();
                fmcAnimation = null;
            }
            container.innerHTML = '';
            container.classList.remove('fallback');
        };
        
        window.testSyncLoader = async function() {
            const container = document.getElementById('test-sync-loader');
            try {
                syncAnimation = await insertLottieLoader(container, {
                    width: 20,
                    height: 20,
                    loop: true,
                    autoplay: true
                });
                console.log('Sync loader started successfully');
            } catch (error) {
                console.error('Failed to start sync loader:', error);
                container.classList.add('fallback');
            }
        };
        
        window.stopSyncLoader = function() {
            const container = document.getElementById('test-sync-loader');
            if (syncAnimation) {
                syncAnimation.destroy();
                syncAnimation = null;
            }
            container.innerHTML = '';
            container.classList.remove('fallback');
        };
    </script>
</body>
</html>
